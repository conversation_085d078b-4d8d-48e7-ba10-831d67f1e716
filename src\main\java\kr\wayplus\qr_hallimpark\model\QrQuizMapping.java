package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

/**
 * QR-문제 매핑 모델
 * - qr_quiz_mapping 테이블에 대응
 * - QR 코드와 문제 간의 연결 정보 관리
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QrQuizMapping {

    /**
     * 매핑 고유 ID
     */
    private Long mappingId;

    /**
     * QR 코드 고유 식별자
     */
    private String qrId;

    /**
     * 문제 ID (FK)
     */
    private Long quizId;

    /**
     * 스토리형 콘텐츠 문제 노출 순서
     */
    private Integer displayOrder;

    /**
     * 생성자 (user_email)
     */
    private String createId;

    /**
     * 생성일시
     */
    private LocalDateTime createDate;

    /**
     * 최종수정자 (user_email)
     */
    private String lastUpdateId;

    /**
     * 최종수정일시
     */
    private LocalDateTime lastUpdateDate;

    /**
     * 삭제여부
     */
    private String deleteYn;

    /**
     * 삭제자 (user_email)
     */
    private String deleteId;

    /**
     * 삭제일시
     */
    private LocalDateTime deleteDate;

    // ========== 조인 필드 (조회 시 사용) ==========

    /**
     * 문제 제목 (quiz_master 조인)
     */
    private String quizTitle;

    /**
     * 문제 유형 (quiz_master 조인)
     */
    private String quizType;

    /**
     * 문제 상태 (quiz_master 조인)
     */
    private String quizStatus;

    /**
     * 카테고리명 (quiz_category 조인)
     */
    private String categoryName;
}
