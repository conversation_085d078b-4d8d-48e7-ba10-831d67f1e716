package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * QR-문제 연결 해제 응답 모델
 * - API 서버로 반환하는 연결 해제 결과 데이터
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignmentDeleteResponse {

    /**
     * 삭제된 매핑 고유 ID
     */
    private Long mappingId;

    /**
     * QR 코드 고유 식별자
     */
    private String qrId;

    /**
     * 문제 ID
     */
    private Long quizId;

    /**
     * 문제 제목
     */
    private String quizTitle;

    /**
     * 삭제 처리 시간
     */
    private LocalDateTime deleteDate;

    /**
     * 삭제자 ID
     */
    private String deleteId;

    /**
     * 삭제 사유
     */
    private String deleteReason;

    /**
     * QrQuizMapping에서 AssignmentDeleteResponse로 변환
     * @param mapping 삭제 전 QrQuizMapping 정보
     * @param deleteReason 삭제 사유
     * @return AssignmentDeleteResponse
     */
    public static AssignmentDeleteResponse fromQrQuizMapping(QrQuizMapping mapping, String deleteReason) {
        return AssignmentDeleteResponse.builder()
                .mappingId(mapping.getMappingId())
                .qrId(mapping.getQrId())
                .quizId(mapping.getQuizId())
                .quizTitle(mapping.getQuizTitle())
                .deleteDate(LocalDateTime.now())
                .deleteId("SYSTEM_API")
                .deleteReason(deleteReason)
                .build();
    }

    /**
     * 간단한 삭제 응답 생성
     * @param mappingId 매핑 ID
     * @param qrId QR 코드 ID
     * @param quizId 문제 ID
     * @return AssignmentDeleteResponse
     */
    public static AssignmentDeleteResponse createSimple(Long mappingId, String qrId, Long quizId) {
        return AssignmentDeleteResponse.builder()
                .mappingId(mappingId)
                .qrId(qrId)
                .quizId(quizId)
                .deleteDate(LocalDateTime.now())
                .deleteId("SYSTEM_API")
                .build();
    }
}
