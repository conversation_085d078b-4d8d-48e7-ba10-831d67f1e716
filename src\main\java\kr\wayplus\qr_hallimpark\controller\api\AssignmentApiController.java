package kr.wayplus.qr_hallimpark.controller.api;

import kr.wayplus.qr_hallimpark.model.AssignmentCreateRequest;
import kr.wayplus.qr_hallimpark.model.AssignmentCreateResponse;
import kr.wayplus.qr_hallimpark.model.QrQuizMapping;
import kr.wayplus.qr_hallimpark.service.QrQuizMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Assignment API 컨트롤러
 * - API 서버에서 호출하는 QR-문제 연결 관련 API
 * - API 키 인증 필요 (WayQRConnect)
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class AssignmentApiController {

    private final QrQuizMappingService qrQuizMappingService;

    /**
     * QR-문제 연결 생성 (API 서버용)
     * @param request 연결 생성 요청
     * @return 연결 생성 결과
     */
    @PostMapping("/assignments")
    public HashMap<String, Object> createAssignment(@RequestBody AssignmentCreateRequest request) {
        log.debug("Creating QR-Quiz assignment: {}", request);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 요청 데이터 유효성 검증
            validateCreateRequest(request);

            // QrQuizMapping 생성 (생성자는 시스템으로 설정)
            QrQuizMapping mapping = request.toQrQuizMapping("SYSTEM_API");

            // 연결 생성
            QrQuizMapping createdMapping = qrQuizMappingService.createMapping(mapping);

            // 생성된 매핑 정보 조회 (조인 정보 포함)
            QrQuizMapping fullMapping = qrQuizMappingService.findMappingById(createdMapping.getMappingId());

            // 응답 데이터 생성
            AssignmentCreateResponse assignmentResponse = AssignmentCreateResponse.fromQrQuizMapping(fullMapping);

            response.put("success", true);
            response.put("message", "연결이 성공적으로 생성되었습니다.");
            response.put("data", assignmentResponse);

            log.info("QR-Quiz assignment created successfully. MappingId: {}, QrId: {}, QuizId: {}", 
                    createdMapping.getMappingId(), request.getQrId(), request.getQuizId());

        } catch (IllegalArgumentException e) {
            log.warn("Invalid assignment request: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_REQUEST");

        } catch (Exception e) {
            log.error("Error creating QR-Quiz assignment: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 생성 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * QR ID로 연결된 문제 목록 조회 (API 서버용)
     * @param qrId QR 코드 ID
     * @return 연결된 문제 목록
     */
    @GetMapping("/assignments/qr/{qrId}")
    public HashMap<String, Object> getAssignmentsByQrId(@PathVariable("qrId") String qrId) {
        log.debug("Getting assignments by QR ID: {}", qrId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<QrQuizMapping> mappings = qrQuizMappingService.findMappingsByQrId(qrId);

            List<AssignmentCreateResponse> assignments = mappings.stream()
                    .map(AssignmentCreateResponse::fromQrQuizMapping)
                    .collect(Collectors.toList());

            response.put("success", true);
            response.put("data", assignments);

            log.debug("Successfully retrieved {} assignments for QR ID: {}", assignments.size(), qrId);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid QR ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_QR_ID");

        } catch (Exception e) {
            log.error("Error retrieving assignments for QR ID {}: {}", qrId, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 정보 조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * QR-문제 연결 삭제 (API 서버용)
     * @param mappingId 매핑 ID
     * @return 삭제 결과
     */
    @DeleteMapping("/assignments/{mappingId}")
    public HashMap<String, Object> deleteAssignment(@PathVariable("mappingId") Long mappingId) {
        log.debug("Deleting assignment by mapping ID: {}", mappingId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            qrQuizMappingService.deleteMapping(mappingId, "SYSTEM_API");

            response.put("success", true);
            response.put("message", "연결이 성공적으로 삭제되었습니다.");

            log.info("QR-Quiz assignment deleted successfully. MappingId: {}", mappingId);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid mapping ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_MAPPING_ID");

        } catch (Exception e) {
            log.error("Error deleting assignment {}: {}", mappingId, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 삭제 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * QR ID로 모든 연결 삭제 (API 서버용)
     * @param qrId QR 코드 ID
     * @return 삭제 결과
     */
    @DeleteMapping("/assignments/qr/{qrId}")
    public HashMap<String, Object> deleteAssignmentsByQrId(@PathVariable("qrId") String qrId) {
        log.debug("Deleting all assignments by QR ID: {}", qrId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            qrQuizMappingService.deleteMappingsByQrId(qrId, "SYSTEM_API");

            response.put("success", true);
            response.put("message", "QR의 모든 연결이 성공적으로 삭제되었습니다.");

            log.info("All QR-Quiz assignments deleted successfully for QR ID: {}", qrId);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid QR ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_QR_ID");

        } catch (Exception e) {
            log.error("Error deleting assignments for QR ID {}: {}", qrId, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 삭제 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 연결 생성 요청 유효성 검증
     * @param request 요청 데이터
     */
    private void validateCreateRequest(AssignmentCreateRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("요청 데이터는 필수입니다.");
        }

        if (request.getQrId() == null || request.getQrId().trim().isEmpty()) {
            throw new IllegalArgumentException("QR ID는 필수입니다.");
        }

        if (request.getQuizId() == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }
    }
}
