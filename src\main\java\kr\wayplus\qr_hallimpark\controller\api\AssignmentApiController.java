package kr.wayplus.qr_hallimpark.controller.api;

import kr.wayplus.qr_hallimpark.model.AssignmentCreateRequest;
import kr.wayplus.qr_hallimpark.model.AssignmentCreateResponse;
import kr.wayplus.qr_hallimpark.model.QrQuizBatchRequest;
import kr.wayplus.qr_hallimpark.model.QrQuizBatchResponse;
import kr.wayplus.qr_hallimpark.model.QrQuizMapping;
import kr.wayplus.qr_hallimpark.service.QrQuizMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Assignment API 컨트롤러
 * - API 서버에서 호출하는 QR-문제 연결 관련 API
 * - API 키 인증 필요 (WayQRConnect)
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class AssignmentApiController {

    private final QrQuizMappingService qrQuizMappingService;

    /**
     * QR-문제 연결 생성 (API 서버용)
     * @param request 연결 생성 요청
     * @return 연결 생성 결과
     */
    @PostMapping("/assignments")
    public HashMap<String, Object> createAssignment(@RequestBody AssignmentCreateRequest request) {
        log.debug("Creating QR-Quiz assignment: {}", request);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 요청 데이터 유효성 검증
            validateCreateRequest(request);

            // QrQuizMapping 생성 (생성자는 시스템으로 설정)
            QrQuizMapping mapping = request.toQrQuizMapping("SYSTEM_API");

            // 연결 생성
            QrQuizMapping createdMapping = qrQuizMappingService.createMapping(mapping);

            // 생성된 매핑 정보 조회 (조인 정보 포함)
            QrQuizMapping fullMapping = qrQuizMappingService.findMappingById(createdMapping.getMappingId());

            // 응답 데이터 생성
            AssignmentCreateResponse assignmentResponse = AssignmentCreateResponse.fromQrQuizMapping(fullMapping);

            response.put("success", true);
            response.put("message", "연결이 성공적으로 생성되었습니다.");
            response.put("data", assignmentResponse);

            log.info("QR-Quiz assignment created successfully. MappingId: {}, QrId: {}, QuizId: {}", 
                    createdMapping.getMappingId(), request.getQrId(), request.getQuizId());

        } catch (IllegalArgumentException e) {
            log.warn("Invalid assignment request: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_REQUEST");

        } catch (Exception e) {
            log.error("Error creating QR-Quiz assignment: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 생성 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * QR ID로 연결된 문제 목록 조회 (API 서버용)
     * @param qrId QR 코드 ID
     * @return 연결된 문제 목록
     */
    @GetMapping("/assignments/qr/{qrId}")
    public HashMap<String, Object> getAssignmentsByQrId(@PathVariable("qrId") String qrId) {
        log.debug("Getting assignments by QR ID: {}", qrId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<QrQuizMapping> mappings = qrQuizMappingService.findMappingsByQrId(qrId);

            List<AssignmentCreateResponse> assignments = mappings.stream()
                    .map(AssignmentCreateResponse::fromQrQuizMapping)
                    .collect(Collectors.toList());

            response.put("success", true);
            response.put("data", assignments);

            log.debug("Successfully retrieved {} assignments for QR ID: {}", assignments.size(), qrId);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid QR ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_QR_ID");

        } catch (Exception e) {
            log.error("Error retrieving assignments for QR ID {}: {}", qrId, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 정보 조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 여러 QR 코드의 연결된 문제 목록 조회 (외부 API용)
     * @param request QR 코드 ID 목록 요청
     * @return QR 코드별 연결된 문제 목록
     */
    @PostMapping("/assignments/batch")
    public HashMap<String, Object> getAssignmentsBatch(@RequestBody QrQuizBatchRequest request) {
        log.debug("Getting assignments batch: {}", request);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 요청 데이터 유효성 검증
            validateBatchRequest(request);

            // 여러 QR ID로 매핑 목록 조회
            List<QrQuizMapping> allMappings = qrQuizMappingService.findMappingsByQrIds(
                    request.getQrIds(),
                    request.getActiveOnly()
            );

            // QR ID별로 그룹화
            Map<String, List<QrQuizMapping>> groupedMappings = allMappings.stream()
                    .collect(Collectors.groupingBy(QrQuizMapping::getQrId));

            // 응답 데이터 생성
            List<QrQuizBatchResponse> batchResponses = request.getQrIds().stream()
                    .map(qrId -> {
                        List<QrQuizMapping> mappings = groupedMappings.getOrDefault(qrId, List.of());
                        List<QrQuizBatchResponse.QrQuizInfo> quizInfos = mappings.stream()
                                .map(QrQuizBatchResponse.QrQuizInfo::fromQrQuizMapping)
                                .collect(Collectors.toList());

                        return QrQuizBatchResponse.builder()
                                .qrId(qrId)
                                .quizzes(quizInfos)
                                .quizCount(quizInfos.size())
                                .build();
                    })
                    .collect(Collectors.toList());

            response.put("success", true);
            response.put("data", batchResponses);
            response.put("totalQrCount", request.getQrIds().size());
            response.put("totalQuizCount", allMappings.size());

            log.debug("Successfully retrieved batch assignments for {} QR codes, {} total quizzes",
                    request.getQrIds().size(), allMappings.size());

        } catch (IllegalArgumentException e) {
            log.warn("Invalid batch request: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_REQUEST");

        } catch (Exception e) {
            log.error("Error retrieving batch assignments: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "배치 조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * QR-문제 연결 삭제 (API 서버용)
     * @param mappingId 매핑 ID
     * @return 삭제 결과
     */
    @DeleteMapping("/assignments/{mappingId}")
    public HashMap<String, Object> deleteAssignment(@PathVariable("mappingId") Long mappingId) {
        log.debug("Deleting assignment by mapping ID: {}", mappingId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            qrQuizMappingService.deleteMapping(mappingId, "SYSTEM_API");

            response.put("success", true);
            response.put("message", "연결이 성공적으로 삭제되었습니다.");

            log.info("QR-Quiz assignment deleted successfully. MappingId: {}", mappingId);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid mapping ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_MAPPING_ID");

        } catch (Exception e) {
            log.error("Error deleting assignment {}: {}", mappingId, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 삭제 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * QR ID로 모든 연결 삭제 (API 서버용)
     * @param qrId QR 코드 ID
     * @return 삭제 결과
     */
    @DeleteMapping("/assignments/qr/{qrId}")
    public HashMap<String, Object> deleteAssignmentsByQrId(@PathVariable("qrId") String qrId) {
        log.debug("Deleting all assignments by QR ID: {}", qrId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            qrQuizMappingService.deleteMappingsByQrId(qrId, "SYSTEM_API");

            response.put("success", true);
            response.put("message", "QR의 모든 연결이 성공적으로 삭제되었습니다.");

            log.info("All QR-Quiz assignments deleted successfully for QR ID: {}", qrId);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid QR ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_QR_ID");

        } catch (Exception e) {
            log.error("Error deleting assignments for QR ID {}: {}", qrId, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 삭제 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 연결 생성 요청 유효성 검증
     * @param request 요청 데이터
     */
    private void validateCreateRequest(AssignmentCreateRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("요청 데이터는 필수입니다.");
        }

        if (request.getQrId() == null || request.getQrId().trim().isEmpty()) {
            throw new IllegalArgumentException("QR ID는 필수입니다.");
        }

        if (request.getQuizId() == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }
    }

    /**
     * 배치 요청 유효성 검증
     * @param request 요청 데이터
     */
    private void validateBatchRequest(QrQuizBatchRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("요청 데이터는 필수입니다.");
        }

        if (request.getQrIds() == null || request.getQrIds().isEmpty()) {
            throw new IllegalArgumentException("QR ID 목록은 필수입니다.");
        }

        if (request.getQrIds().size() > 100) {
            throw new IllegalArgumentException("한 번에 조회할 수 있는 QR 코드는 최대 100개입니다.");
        }

        // 빈 문자열이나 null 값 체크
        long validQrIdCount = request.getQrIds().stream()
                .filter(qrId -> qrId != null && !qrId.trim().isEmpty())
                .count();

        if (validQrIdCount == 0) {
            throw new IllegalArgumentException("유효한 QR ID가 없습니다.");
        }
    }
}
