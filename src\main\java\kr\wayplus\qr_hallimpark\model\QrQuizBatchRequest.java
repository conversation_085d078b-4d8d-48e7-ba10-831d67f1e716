package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 여러 QR 코드의 연결된 문제 조회 요청 모델
 * - 외부에서 여러 QR 코드 ID를 JSON으로 전송할 때 사용
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QrQuizBatchRequest {

    /**
     * QR 코드 ID 목록
     * - 조회하고자 하는 QR 코드들의 ID 배열
     */
    private List<String> qrIds;

    /**
     * 정렬 옵션 (선택사항)
     * - "displayOrder": 노출 순서별 정렬 (기본값)
     * - "createDate": 생성일시별 정렬
     */
    private String sortBy;

    /**
     * 활성화된 문제만 조회할지 여부 (선택사항)
     * - true: 활성화된 문제만 조회 (기본값)
     * - false: 모든 문제 조회
     */
    @Builder.Default
    private Boolean activeOnly = true;
}
