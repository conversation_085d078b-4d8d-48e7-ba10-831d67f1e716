package kr.wayplus.qr_hallimpark.controller.api;

import kr.wayplus.qr_hallimpark.model.AssignmentDeleteRequest;
import kr.wayplus.qr_hallimpark.model.AssignmentDeleteResponse;
import kr.wayplus.qr_hallimpark.model.QrQuizBatchRequest;
import kr.wayplus.qr_hallimpark.model.QrQuizBatchResponse;
import kr.wayplus.qr_hallimpark.model.QrQuizMapping;
import kr.wayplus.qr_hallimpark.service.QrQuizMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Public Quiz API 컨트롤러
 * - 외부 프론트엔드에서 호출하는 공개 API (API 키 인증 불필요)
 * - 읽기 전용 작업만 허용 (보안 강화)
 * - CORS 설정으로 특정 도메인만 허용
 */
@Slf4j
@RestController
@RequestMapping("/api/public")
@RequiredArgsConstructor
@CrossOrigin(
    origins = {"http://192.168.0.227:9998"},
    allowedHeaders = {"*"},
    methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS},
    allowCredentials = "true",
    maxAge = 3600
)
public class PublicQuizApiController {

    private final QrQuizMappingService qrQuizMappingService;

    /**
     * 여러 QR 코드의 연결된 문제 목록 조회 (공개 API)
     * - API 키 인증 불필요
     * - 읽기 전용 (조회만 가능)
     * - 활성화된 문제만 조회
     *
     * @param request QR 코드 ID 목록 요청
     * @return QR 코드별 연결된 문제 목록
     */
    @PostMapping({"/quiz-assignments/batch", "/assignments/batch"})
    public HashMap<String, Object> getQuizAssignmentsBatch(@RequestBody QrQuizBatchRequest request) {
        log.debug("Getting public quiz assignments batch: {}", request);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 요청 데이터 유효성 검증
            validatePublicBatchRequest(request);

            // 보안을 위해 활성화된 문제만 강제 조회
            List<QrQuizMapping> allMappings = qrQuizMappingService.findMappingsByQrIds(
                    request.getQrIds(), 
                    true // 항상 활성화된 문제만 조회
            );

            // QR ID별로 그룹화
            Map<String, List<QrQuizMapping>> groupedMappings = allMappings.stream()
                    .collect(Collectors.groupingBy(QrQuizMapping::getQrId));

            // 응답 데이터 생성 (민감한 정보 제외)
            List<QrQuizBatchResponse> batchResponses = request.getQrIds().stream()
                    .map(qrId -> {
                        List<QrQuizMapping> mappings = groupedMappings.getOrDefault(qrId, List.of());
                        List<QrQuizBatchResponse.QrQuizInfo> quizInfos = mappings.stream()
                                .map(this::createSafeQuizInfo) // 민감한 정보 제거
                                .collect(Collectors.toList());

                        return QrQuizBatchResponse.builder()
                                .qrId(qrId)
                                .quizzes(quizInfos)
                                .quizCount(quizInfos.size())
                                .build();
                    })
                    .collect(Collectors.toList());

            response.put("success", true);
            response.put("data", batchResponses);
            response.put("totalQrCount", request.getQrIds().size());
            response.put("totalQuizCount", allMappings.size());

            log.debug("Successfully retrieved public batch assignments for {} QR codes, {} total quizzes", 
                    request.getQrIds().size(), allMappings.size());

        } catch (IllegalArgumentException e) {
            log.warn("Invalid public batch request: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_REQUEST");

        } catch (Exception e) {
            log.error("Error retrieving public batch assignments: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 단일 QR 코드의 연결된 문제 목록 조회 (공개 API)
     * @param qrId QR 코드 ID
     * @return 연결된 문제 목록
     */
    @GetMapping("/quiz-assignments/qr/{qrId}")
    public HashMap<String, Object> getQuizAssignmentsByQrId(@PathVariable("qrId") String qrId) {
        log.debug("Getting public quiz assignments by QR ID: {}", qrId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            if (qrId == null || qrId.trim().isEmpty()) {
                throw new IllegalArgumentException("QR ID는 필수입니다.");
            }

            // 활성화된 문제만 조회
            List<QrQuizMapping> mappings = qrQuizMappingService.findMappingsByQrId(qrId)
                    .stream()
                    .filter(mapping -> "ACTIVE".equals(mapping.getQuizStatus()))
                    .collect(Collectors.toList());

            List<QrQuizBatchResponse.QrQuizInfo> quizInfos = mappings.stream()
                    .map(this::createSafeQuizInfo)
                    .collect(Collectors.toList());

            QrQuizBatchResponse batchResponse = QrQuizBatchResponse.builder()
                    .qrId(qrId)
                    .quizzes(quizInfos)
                    .quizCount(quizInfos.size())
                    .build();

            response.put("success", true);
            response.put("data", batchResponse);

            log.debug("Successfully retrieved {} public assignments for QR ID: {}", quizInfos.size(), qrId);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid QR ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_QR_ID");

        } catch (Exception e) {
            log.error("Error retrieving public assignments for QR ID {}: {}", qrId, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * QR-문제 연결 해제 (공개 API)
     * - 외부 프론트엔드에서 안전하게 연결 해제
     * - 읽기 전용이 아닌 제한적 쓰기 작업
     * @param request 연결 해제 요청
     * @return 연결 해제 결과
     */
    @PostMapping({"/quiz-assignments/unlink", "/assignments/unlink"})
    public HashMap<String, Object> unlinkQuizAssignment(@RequestBody AssignmentDeleteRequest request) {
        log.debug("Unlinking public quiz assignment: {}", request);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 공개 API용 유효성 검증 (더 제한적)
            validatePublicDeleteRequest(request);

            QrQuizMapping deletedMapping;
            AssignmentDeleteResponse deleteResponse;

            if (request.isDirectMappingDelete()) {
                // 매핑 ID로 직접 삭제
                deletedMapping = qrQuizMappingService.findMappingById(request.getMappingId());

                // 활성화된 문제만 해제 가능 (보안 강화)
                if (!"ACTIVE".equals(deletedMapping.getQuizStatus())) {
                    throw new IllegalArgumentException("비활성화된 문제의 연결은 해제할 수 없습니다.");
                }

                qrQuizMappingService.deleteMapping(request.getMappingId(), "PUBLIC_API");
                deleteResponse = AssignmentDeleteResponse.fromQrQuizMapping(deletedMapping, request.getDeleteReason());

                log.info("Public QR-Quiz assignment unlinked by mappingId. MappingId: {}", request.getMappingId());

            } else {
                // QR ID와 문제 ID로 삭제
                deletedMapping = qrQuizMappingService.deleteMappingByQrIdAndQuizId(
                        request.getQrId(), request.getQuizId(), "PUBLIC_API");

                deleteResponse = AssignmentDeleteResponse.fromQrQuizMapping(deletedMapping, request.getDeleteReason());

                log.info("Public QR-Quiz assignment unlinked by QrId and QuizId. QrId: {}, QuizId: {}, MappingId: {}",
                        request.getQrId(), request.getQuizId(), deletedMapping.getMappingId());
            }

            response.put("success", true);
            response.put("message", "연결이 성공적으로 해제되었습니다.");
            response.put("data", deleteResponse);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid public unlink request: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_REQUEST");

        } catch (Exception e) {
            log.error("Error unlinking public quiz assignment: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 해제 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * CORS 테스트용 간단한 엔드포인트
     * @return 성공 응답
     */
    @GetMapping("/test")
    public HashMap<String, Object> testCors() {
        HashMap<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "CORS test successful");
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * CORS Preflight 요청 처리 (OPTIONS 메서드)
     * @return 성공 응답
     */
    @RequestMapping(value = "/**", method = RequestMethod.OPTIONS)
    public HashMap<String, Object> handleOptions() {
        HashMap<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "CORS preflight request handled successfully");
        return response;
    }

    /**
     * 공개 API용 안전한 문제 정보 생성 (민감한 정보 제외)
     * @param mapping QrQuizMapping 객체
     * @return 안전한 QrQuizInfo 객체
     */
    private QrQuizBatchResponse.QrQuizInfo createSafeQuizInfo(QrQuizMapping mapping) {
        return QrQuizBatchResponse.QrQuizInfo.builder()
                .mappingId(mapping.getMappingId())
                .quizId(mapping.getQuizId())
                .quizTitle(mapping.getQuizTitle())
                .question(mapping.getQuestion())
                .quizType(mapping.getQuizType())
                .quizStatus(mapping.getQuizStatus())
                .categoryName(mapping.getCategoryName())
                .difficultyLevel(mapping.getDifficultyLevel())
                .displayOrder(mapping.getDisplayOrder())
                // 보안상 민감한 정보는 제외
                .correctAnswer(null) // 정답은 공개하지 않음
                .imageUrl(mapping.getImageUrl())
                .hint(mapping.getHint())
                .build();
    }

    /**
     * 공개 API 배치 요청 유효성 검증
     * @param request 요청 데이터
     */
    private void validatePublicBatchRequest(QrQuizBatchRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("요청 데이터는 필수입니다.");
        }

        if (request.getQrIds() == null || request.getQrIds().isEmpty()) {
            throw new IllegalArgumentException("QR ID 목록은 필수입니다.");
        }

        if (request.getQrIds().size() > 50) { // 공개 API는 더 제한적
            throw new IllegalArgumentException("한 번에 조회할 수 있는 QR 코드는 최대 50개입니다.");
        }

        // 빈 문자열이나 null 값 체크
        long validQrIdCount = request.getQrIds().stream()
                .filter(qrId -> qrId != null && !qrId.trim().isEmpty())
                .count();

        if (validQrIdCount == 0) {
            throw new IllegalArgumentException("유효한 QR ID가 없습니다.");
        }
    }

    /**
     * 공개 API 연결 해제 요청 유효성 검증
     * @param request 요청 데이터
     */
    private void validatePublicDeleteRequest(AssignmentDeleteRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("요청 데이터는 필수입니다.");
        }

        if (!request.isValid()) {
            throw new IllegalArgumentException("매핑 ID 또는 QR ID와 문제 ID가 필요합니다.");
        }

        // 매핑 ID로 삭제하는 경우
        if (request.isDirectMappingDelete()) {
            if (request.getMappingId() <= 0) {
                throw new IllegalArgumentException("유효하지 않은 매핑 ID입니다.");
            }
        } else {
            // QR ID와 문제 ID로 삭제하는 경우
            if (request.getQrId().trim().length() > 100) {
                throw new IllegalArgumentException("QR ID가 너무 깁니다. (최대 100자)");
            }

            if (request.getQuizId() <= 0) {
                throw new IllegalArgumentException("유효하지 않은 문제 ID입니다.");
            }
        }

        // 공개 API는 삭제 사유 길이 제한 (보안상)
        if (request.getDeleteReason() != null && request.getDeleteReason().length() > 200) {
            throw new IllegalArgumentException("삭제 사유가 너무 깁니다. (최대 200자)");
        }
    }
}
