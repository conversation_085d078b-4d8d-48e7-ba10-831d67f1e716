<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.wayplus.qr_hallimpark.mapper.QrQuizMappingMapper">

    <!-- 기본 ResultMap -->
    <resultMap id="QrQuizMappingResultMap" type="kr.wayplus.qr_hallimpark.model.QrQuizMapping">
        <id property="mappingId" column="mapping_id"/>
        <result property="qrId" column="qr_id"/>
        <result property="quizId" column="quiz_id"/>
        <result property="displayOrder" column="display_order"/>
        <result property="createId" column="create_id"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdateId" column="last_update_id"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteYn" column="delete_yn"/>
        <result property="deleteId" column="delete_id"/>
        <result property="deleteDate" column="delete_date"/>
    </resultMap>

    <!-- 조인 ResultMap (문제 정보 포함) -->
    <resultMap id="QrQuizMappingWithQuizResultMap" type="kr.wayplus.qr_hallimpark.model.QrQuizMapping" extends="QrQuizMappingResultMap">
        <result property="quizTitle" column="quiz_title"/>
        <result property="quizType" column="quiz_type"/>
        <result property="quizStatus" column="quiz_status"/>
        <result property="categoryName" column="category_name"/>
        <result property="question" column="question"/>
        <result property="difficultyLevel" column="difficulty_level"/>
        <result property="correctAnswer" column="correct_answer"/>
        <result property="imageUrl" column="image_url"/>
        <result property="hint" column="hint"/>
    </resultMap>

    <!-- QR ID로 매핑 목록 조회 -->
    <select id="selectMappingsByQrId" resultMap="QrQuizMappingWithQuizResultMap">
        SELECT
            qm.mapping_id,
            qm.qr_id,
            qm.quiz_id,
            qm.display_order,
            qm.create_id,
            qm.create_date,
            qm.last_update_id,
            qm.last_update_date,
            qm.delete_yn,
            qm.delete_id,
            qm.delete_date,
            q.title AS quiz_title,
            q.quiz_type,
            q.status AS quiz_status,
            q.difficulty_level,
            q.correct_answer,
            q.image_url,
            q.hint,
            c.category_name,
            qc.question
        FROM qr_quiz_mapping qm
        INNER JOIN quiz_master q ON qm.quiz_id = q.quiz_id AND q.delete_yn = 'N'
        INNER JOIN quiz_category c ON q.category_id = c.category_id AND c.delete_yn = 'N'
        LEFT JOIN quiz_content qc ON q.quiz_id = qc.quiz_id AND qc.lang_code = 'ko' AND qc.delete_yn = 'N'
        WHERE qm.qr_id = #{qrId}
        AND qm.delete_yn = 'N'
        ORDER BY qm.display_order ASC, qm.create_date ASC
    </select>

    <!-- 여러 QR ID로 매핑 목록 조회 -->
    <select id="selectMappingsByQrIds" resultMap="QrQuizMappingWithQuizResultMap">
        SELECT
            qm.mapping_id,
            qm.qr_id,
            qm.quiz_id,
            qm.display_order,
            qm.create_id,
            qm.create_date,
            qm.last_update_id,
            qm.last_update_date,
            qm.delete_yn,
            qm.delete_id,
            qm.delete_date,
            q.title AS quiz_title,
            q.quiz_type,
            q.status AS quiz_status,
            q.difficulty_level,
            q.correct_answer,
            q.image_url,
            q.hint,
            c.category_name,
            qc.question
        FROM qr_quiz_mapping qm
        INNER JOIN quiz_master q ON qm.quiz_id = q.quiz_id AND q.delete_yn = 'N'
        INNER JOIN quiz_category c ON q.category_id = c.category_id AND c.delete_yn = 'N'
        LEFT JOIN quiz_content qc ON q.quiz_id = qc.quiz_id AND qc.lang_code = 'ko' AND qc.delete_yn = 'N'
        WHERE qm.qr_id IN
        <foreach collection="qrIds" item="qrId" open="(" separator="," close=")">
            #{qrId}
        </foreach>
        AND qm.delete_yn = 'N'
        <if test="activeOnly != null and activeOnly == true">
            AND q.status = 'ACTIVE'
        </if>
        ORDER BY qm.qr_id, qm.display_order ASC, qm.create_date ASC
    </select>

    <!-- 문제 ID로 매핑 목록 조회 -->
    <select id="selectMappingsByQuizId" resultMap="QrQuizMappingResultMap">
        SELECT
            mapping_id,
            qr_id,
            quiz_id,
            display_order,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM qr_quiz_mapping
        WHERE quiz_id = #{quizId}
        AND delete_yn = 'N'
        ORDER BY create_date DESC
    </select>

    <!-- 매핑 ID로 단일 매핑 조회 -->
    <select id="selectMappingById" resultMap="QrQuizMappingWithQuizResultMap">
        SELECT
            qm.mapping_id,
            qm.qr_id,
            qm.quiz_id,
            qm.display_order,
            qm.create_id,
            qm.create_date,
            qm.last_update_id,
            qm.last_update_date,
            qm.delete_yn,
            qm.delete_id,
            qm.delete_date,
            q.title AS quiz_title,
            q.quiz_type,
            q.status AS quiz_status,
            c.category_name
        FROM qr_quiz_mapping qm
        INNER JOIN quiz_master q ON qm.quiz_id = q.quiz_id AND q.delete_yn = 'N'
        INNER JOIN quiz_category c ON q.category_id = c.category_id AND c.delete_yn = 'N'
        WHERE qm.mapping_id = #{mappingId}
        AND qm.delete_yn = 'N'
    </select>

    <!-- QR ID와 문제 ID로 매핑 조회 -->
    <select id="selectMappingByQrIdAndQuizId" resultMap="QrQuizMappingResultMap">
        SELECT
            mapping_id,
            qr_id,
            quiz_id,
            display_order,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM qr_quiz_mapping
        WHERE qr_id = #{qrId}
        AND quiz_id = #{quizId}
        AND delete_yn = 'N'
    </select>

    <!-- 매핑 등록 -->
    <insert id="insertMapping" parameterType="kr.wayplus.qr_hallimpark.model.QrQuizMapping" useGeneratedKeys="true" keyProperty="mappingId">
        INSERT INTO qr_quiz_mapping (
            qr_id,
            quiz_id,
            display_order,
            create_id,
            delete_yn
        ) VALUES (
            #{qrId},
            #{quizId},
            #{displayOrder},
            #{createId},
            'N'
        )
    </insert>

    <!-- 매핑 수정 -->
    <update id="updateMapping" parameterType="kr.wayplus.qr_hallimpark.model.QrQuizMapping">
        UPDATE qr_quiz_mapping
        SET
            display_order = #{displayOrder},
            last_update_id = #{lastUpdateId}
        WHERE mapping_id = #{mappingId}
        AND delete_yn = 'N'
    </update>

    <!-- 매핑 삭제 (논리 삭제) -->
    <update id="deleteMapping">
        UPDATE qr_quiz_mapping
        SET
            delete_yn = 'Y',
            delete_id = #{deleteId},
            delete_date = NOW()
        WHERE mapping_id = #{mappingId}
        AND delete_yn = 'N'
    </update>

    <!-- QR ID로 모든 매핑 삭제 (논리 삭제) -->
    <update id="deleteMappingsByQrId">
        UPDATE qr_quiz_mapping
        SET
            delete_yn = 'Y',
            delete_id = #{deleteId},
            delete_date = NOW()
        WHERE qr_id = #{qrId}
        AND delete_yn = 'N'
    </update>

    <!-- 문제 ID로 모든 매핑 삭제 (논리 삭제) -->
    <update id="deleteMappingsByQuizId">
        UPDATE qr_quiz_mapping
        SET
            delete_yn = 'Y',
            delete_id = #{deleteId},
            delete_date = NOW()
        WHERE quiz_id = #{quizId}
        AND delete_yn = 'N'
    </update>

</mapper>
