package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 여러 QR 코드의 연결된 문제 조회 응답 모델
 * - QR 코드별로 연결된 문제 목록을 그룹화하여 반환
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QrQuizBatchResponse {

    /**
     * QR 코드 ID
     */
    private String qrId;

    /**
     * 해당 QR 코드에 연결된 문제 목록
     */
    private List<QrQuizInfo> quizzes;

    /**
     * 연결된 문제 개수
     */
    private Integer quizCount;

    /**
     * QR 코드별 문제 정보
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QrQuizInfo {
        
        /**
         * 매핑 고유 ID
         */
        private Long mappingId;
        
        /**
         * 문제 ID
         */
        private Long quizId;
        
        /**
         * 문제 제목
         */
        private String quizTitle;
        
        /**
         * 문제 질문 내용 (한국어)
         */
        private String question;
        
        /**
         * 문제 유형 (MCQ, OX, ORDER 등)
         */
        private String quizType;
        
        /**
         * 문제 상태 (ACTIVE, INACTIVE)
         */
        private String quizStatus;
        
        /**
         * 카테고리명
         */
        private String categoryName;
        
        /**
         * 난이도 (1~5)
         */
        private Integer difficultyLevel;
        
        /**
         * 노출 순서
         */
        private Integer displayOrder;
        
        /**
         * 정답 (JSON, 텍스트 등)
         */
        private String correctAnswer;
        
        /**
         * 문제 이미지 경로
         */
        private String imageUrl;
        
        /**
         * 힌트 내용
         */
        private String hint;

        /**
         * QrQuizMapping에서 QrQuizInfo로 변환
         * @param mapping QrQuizMapping 객체
         * @return QrQuizInfo 객체
         */
        public static QrQuizInfo fromQrQuizMapping(QrQuizMapping mapping) {
            return QrQuizInfo.builder()
                    .mappingId(mapping.getMappingId())
                    .quizId(mapping.getQuizId())
                    .quizTitle(mapping.getQuizTitle())
                    .question(mapping.getQuestion())
                    .quizType(mapping.getQuizType())
                    .quizStatus(mapping.getQuizStatus())
                    .categoryName(mapping.getCategoryName())
                    .difficultyLevel(mapping.getDifficultyLevel())
                    .displayOrder(mapping.getDisplayOrder())
                    .correctAnswer(mapping.getCorrectAnswer())
                    .imageUrl(mapping.getImageUrl())
                    .hint(mapping.getHint())
                    .build();
        }
    }
}
