package kr.wayplus.qr_hallimpark.config;

import kr.wayplus.qr_hallimpark.common.utils.CustomBcryptPasswordEncoder;
import kr.wayplus.qr_hallimpark.config.handler.LoginFailureHandler;
import kr.wayplus.qr_hallimpark.config.handler.LoginSuccessHandler;
import kr.wayplus.qr_hallimpark.config.handler.ManageLoginFailureHandler;
import kr.wayplus.qr_hallimpark.config.handler.ManageLoginSuccessHandler;
import kr.wayplus.qr_hallimpark.config.handler.SecurityAccessDeniedHandler;
import kr.wayplus.qr_hallimpark.service.UserService;
import kr.wayplus.qr_hallimpark.service.manage.ManageUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.core.annotation.Order; // Order 임포트 추가
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@Configuration
@EnableWebSecurity
public class SecurityConfig {
	@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")

	@Autowired
	LoginSuccessHandler loginSuccessHandler;

	@Autowired
	LoginFailureHandler loginFailureHandler;

	@Autowired
	ManageLoginSuccessHandler manageLoginSuccessHandler;

	@Autowired
	ManageLoginFailureHandler manageLoginFailureHandler;

	@Autowired
	UserService userService;

	@Autowired
	ManageUserService manageUserService;

	@Bean
	public PasswordEncoder passwordEncoder() {
		return new CustomBcryptPasswordEncoder();
	}

	/**
	 * 관리자 인증 프로바이더
	 */
	@Bean
	public DaoAuthenticationProvider adminAuthenticationProvider() {
		DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
		authProvider.setUserDetailsService(manageUserService);
		authProvider.setPasswordEncoder(passwordEncoder());
		return authProvider;
	}

	/**
	 * 사용자 인증 프로바이더
	 */
	@Bean
	public DaoAuthenticationProvider userAuthenticationProvider() {
		DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
		authProvider.setUserDetailsService(userService);
		authProvider.setPasswordEncoder(passwordEncoder());
		return authProvider;
	}

	@Bean
	@Order(1) // 관리자 필터 체인 우선순위
	public SecurityFilterChain adminFilterChain(HttpSecurity http) throws Exception { // 메소드 이름도 adminFilterChain으로 통일
		http.securityMatcher("/manage/**") // antMatcher -> securityMatcher
			.authenticationProvider(adminAuthenticationProvider()) // 관리자 인증 프로바이더 설정
			.authorizeHttpRequests(authz -> authz
				.requestMatchers("/manage/login", "/manage/login-progress").permitAll()
				.anyRequest().hasRole("ADMIN")
			)
			.formLogin(form -> form
				.loginPage("/manage/login")
				.loginProcessingUrl("/manage/login-security")
				.usernameParameter("admin-email")
				.passwordParameter("admin-pass")
				.successHandler(manageLoginSuccessHandler)
				.failureHandler(manageLoginFailureHandler)
				.permitAll()
			)
			.logout(logout -> logout
				.logoutUrl("/manage/logout")
				.logoutSuccessUrl("/manage/login?logout=y")
				.invalidateHttpSession(true)
				.deleteCookies("JSESSIONID")
				.permitAll()
			)
			.exceptionHandling(exceptions -> exceptions
				.accessDeniedHandler(new SecurityAccessDeniedHandler()) // 권한 없는 경우
				.authenticationEntryPoint((request, response, authException) -> { // 인증 안된 경우
					response.sendRedirect(request.getContextPath() + "/manage/login");
				})
			)
			.sessionManagement(session -> session
				.sessionCreationPolicy(SessionCreationPolicy.ALWAYS)
				.maximumSessions(1)
				.expiredUrl("/manage/login?error=expired") // 세션 만료 시 관리자 로그인 페이지로 이동
			)
			.csrf(csrf -> csrf
				.ignoringRequestMatchers("/manage/login-progress", "/manage/login-security") // 관리자 로그인 진행은 CSRF 제외
			);
		return http.build();
	}

	@Bean
@Order(2) // 사용자 필터 체인 우선순위
    public SecurityFilterChain userFilterChain(HttpSecurity http) throws Exception {
        http
            // 이 필터 체인은 /manage/** 를 제외한 모든 요청에 적용될 수 있도록 securityMatcher를 명시하지 않거나, 넓은 범위를 지정합니다.
            // 여기서는 명시하지 않아 기본 필터 체인으로 동작하도록 합니다.
            .authenticationProvider(userAuthenticationProvider()) // 사용자 인증 프로바이더 설정
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/favicon.ico", "/error").permitAll()
                .requestMatchers("/", "/main", "/index").permitAll()
                .requestMatchers("/user/login", "/user/login-progress", "/user/join", "/user/join-progress", "/user/find-id", "/user/find-pass").permitAll() // 사용자 인증 관련 페이지
                .requestMatchers("/user/session-expired").permitAll()
                .requestMatchers("/static/**", "/css/**", "/js/**", "/images/**", "/files/**", "/upload/**", "/qrstandard/**").permitAll() // 정적 리소스
                // API 경로 제거 - 일반 컨트롤러 엔드포인트 사용
                .requestMatchers("/user/**", "/view/**", "/contents/**").authenticated() // 그 외 사용자 관련 경로는 인증 필요
                .anyRequest().permitAll() // 그 외 모든 요청은 일단 허용 (개발 중 편의, 추후 .denyAll() 또는 .authenticated()로 변경 고려)
            )
            .formLogin(form -> form
                .loginPage("/user/login")
                .loginProcessingUrl("/user/login-progress")
                .usernameParameter("user-email")
                .passwordParameter("user-pass")
                .successHandler(loginSuccessHandler)
                .failureHandler(loginFailureHandler)
                .permitAll()
            )
            .logout(logout -> logout
                .logoutUrl("/user/logout")
                .logoutSuccessUrl("/")
                .invalidateHttpSession(true)
                .deleteCookies("JSESSIONID", "hallimpark") // app.cookie.name 값 사용 고려
                .permitAll()
            )
            .exceptionHandling(exceptions -> exceptions
                .accessDeniedHandler(new SecurityAccessDeniedHandler()) // 권한 없는 경우
                .authenticationEntryPoint((request, response, authException) -> { // 인증 안된 경우 (사용자 경로)
                    // /manage/** 경로는 adminFilterChain에서 처리되므로 여기서는 사용자 경로만 고려
                    response.sendRedirect(request.getContextPath() + "/user/login");
                })
            )
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED) // 필요 시 세션 생성
                .sessionFixation().changeSessionId()
                .maximumSessions(1)
                .expiredUrl("/user/login?error=expired") // 사용자 세션 만료 시
            )
            .csrf(csrf -> csrf
                // 사용자 로그인 처리 경로는 CSRF 보호 예외
                .ignoringRequestMatchers("/user/login-progress", "/user/join-progress")
            );

        return http.build();
    }

	@Bean
	public CorsConfigurationSource corsConfigurationSource(){
		CorsConfiguration navigatorApiConfiguration = new CorsConfiguration();
		navigatorApiConfiguration.addAllowedOriginPattern("*");
		navigatorApiConfiguration.addAllowedOrigin("Accept-*");
		navigatorApiConfiguration.addAllowedMethod(HttpMethod.POST);
		navigatorApiConfiguration.addAllowedMethod(HttpMethod.GET);
		navigatorApiConfiguration.addAllowedHeader("*");
		navigatorApiConfiguration.addAllowedHeader("Origin");
		navigatorApiConfiguration.setAllowCredentials(true);

		UrlBasedCorsConfigurationSource configurationSource = new UrlBasedCorsConfigurationSource();
		// API 경로 제거 - 필요시 특정 엔드포인트별로 CORS 설정
		// configurationSource.registerCorsConfiguration("/specific-endpoint/**" , navigatorApiConfiguration);
		return configurationSource;
	}
}
